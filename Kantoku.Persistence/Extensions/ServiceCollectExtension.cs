using Kantoku.Persistence.Contexts;
using Kantoku.Persistence.Repositories;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;
using Microsoft.Extensions.DependencyInjection;
using Kantoku.Persistence.Seeders.Categories;
using Kantoku.Persistence.Seeders.CustomerType;

namespace Kantoku.Persistence
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddDbContext(
            this IServiceCollection services
        )
        {
            services.AddDbContext<PostgreDbContext>(options =>
            {
                options.EnableDetailedErrors();
                options.EnableSensitiveDataLogging();
            });
            return services;
        }

        public static IServiceCollection AddRepositories(
            this IServiceCollection services
        )
        {
            services.AddScoped<IAccountRepository, AccountRepository>();
            services.AddScoped<IAuditLogRepository, AuditLogRepository>();
            services.AddScoped<ICategoryRepository, CategoryRepository>();
            services.AddScoped<IConstructionCostRepository, ConstructionCostRepository>();
            services.AddScoped<IConstructionRepository, ConstructionRepository>();
            services.AddScoped<IContractorRepository, ContractorRepository>();
            services.AddScoped<ICustomerRepository, CustomerRepository>();
            services.AddScoped<ICustomerTypeRepository, CustomerTypeRepository>();
            services.AddScoped<IDeviceTokenRepository, DeviceTokenRepository>();
            services.AddScoped<IEmpContractRepository, EmpContractRepository>();
            services.AddScoped<IEmployeeInvitationRepository, EmployeeInvitationRepository>();
            services.AddScoped<IEmployeeLeaveRepository, EmployeeLeaveRepository>();
            services.AddScoped<IEmployeeNotificationRepository, EmployeeNotificationRepository>();
            services.AddScoped<IEmployeeRepository, EmployeeRepository>();
            services.AddScoped<IEmployeeShiftRepository, EmployeeShiftRepository>();
            services.AddScoped<IEntryTypeRepository, EntryTypeRepository>();
            services.AddScoped<IEventCalendarRepository, EventCalendarRepository>();
            services.AddScoped<IFileRepository, FileRepository>();
            services.AddScoped<IFunctionRepository, FunctionRepository>();
            services.AddScoped<IGlobalConfigRepository, GlobalConfigRepository>();
            services.AddScoped<IInputCostItemRepository, InputCostItemRepository>();
            services.AddScoped<IInputCostRepository, InputCostRepository>();
            services.AddScoped<IItemPriceRepository, ItemPriceRepository>();
            services.AddScoped<IItemRepository, ItemRepository>();
            services.AddScoped<ILanguageRepository, LanguageRepository>();
            services.AddScoped<ILeaveTypeRepository, LeaveTypeRepository>();
            services.AddScoped<IManufacturerRepository, ManufacturerRepository>();
            services.AddScoped<IMonthlyReportRepository, MonthlyReportRepository>();
            services.AddScoped<INotificationRepository, NotificationRepository>();
            services.AddScoped<IOrgRepository, OrgRepository>();
            services.AddScoped<IOutSourcePriceRepository, OutSourcePriceRepository>();
            services.AddScoped<IOutSourceRepository, OutSourceRepository>();
            services.AddScoped<IOutSourceShiftRepository, OutSourceShiftRepository>();
            services.AddScoped<IPaymentTypeRepository, PaymentTypeRepository>();
            services.AddScoped<IPositionRepository, PositionRepository>();
            services.AddScoped<IProjectDailyReportRepository, ProjectDailyReportRepository>();
            services.AddScoped<IProjectRankingCostRepository, ProjectRankingCostRepository>();
            services.AddScoped<IProjectRepository, ProjectRepository>();
            services.AddScoped<IProjectScheduleRepository, ProjectScheduleRepository>();
            services.AddScoped<IProjectTypeRepository, ProjectTypeRepository>();
            services.AddScoped<IRankingRepository, RankingRepository>();
            services.AddScoped<IRequestRepository, RequestRepository>();
            services.AddScoped<IRequestTypeRepository, RequestTypeRepository>();
            services.AddScoped<IRoleFunctionRepository, RoleFunctionRepository>();
            services.AddScoped<IRoleRepository, RoleRepository>();
            services.AddScoped<IStatusRepository, StatusRepository>();
            services.AddScoped<IStructureRepository, StructureRepository>();
            services.AddScoped<IUserInfoRepository, UserInfoRepository>();
            services.AddScoped<IVendorRepository, VendorRepository>();
            services.AddScoped<IWorkShiftRepository, WorkShiftRepository>();
            return services;
        }

        public static IServiceCollection AddQueryables(
            this IServiceCollection services
        )
        {
            services.AddScoped<IAccountQueryable, AccountQueryable>();
            services.AddScoped<IAuditLogQueryable, AuditLogQueryable>();
            services.AddScoped<ICategoryQueryable, CategoryQueryable>();
            services.AddScoped<IConstructionCostQueryable, ConstructionCostQueryable>();
            services.AddScoped<IConstructionQueryable, ConstructionQueryable>();
            services.AddScoped<IContractorQueryable, ContractorQueryable>();
            services.AddScoped<ICustomerQueryable, CustomerQueryable>();
            services.AddScoped<IDeviceTokenQueryable, DeviceTokenQueryable>();
            services.AddScoped<IEmployeeInvitationQueryable, EmployeeInvitationQueryable>();
            services.AddScoped<IEmployeeLeaveQueryable, EmployeeLeaveQueryable>();
            services.AddScoped<IEmployeeNotificationQueryable, EmployeeNotificationQueryable>();
            services.AddScoped<IEmployeeQueryable, EmployeeQueryable>();
            services.AddScoped<IEmployeeCostQueryable, EmployeeCostQueryable>();
            services.AddScoped<IEmployeeShiftQueryable, EmployeeShiftQueryable>();
            services.AddScoped<IEntryTypeQueryable, EntryTypeQueryable>();
            services.AddScoped<IEventCalendarQueryable, EventCalendarQueryable>();
            services.AddScoped<IInputCostItemQueryable, InputCostItemQueryable>();
            services.AddScoped<IInputCostQueryable, InputCostQueryable>();
            services.AddScoped<IItemPriceQueryable, ItemPriceQueryable>();
            services.AddScoped<IItemQueryable, ItemQueryable>();
            services.AddScoped<IManufacturerQueryable, ManufacturerQueryable>();
            services.AddScoped<IMonthlyReportQueryable, MonthlyReportQueryable>();
            services.AddScoped<INotificationQueryable, NotificationQueryable>();
            services.AddScoped<IOrgQueryable, OrgQueryable>();
            services.AddScoped<IOutSourcePriceQueryable, OutSourcePriceQueryable>();
            services.AddScoped<IOutSourceQueryable, OutSourceQueryable>();
            services.AddScoped<IOutSourceShiftQueryable, OutSourceShiftQueryable>();
            services.AddScoped<IPaymentTypeQueryable, PaymentTypeQueryable>();
            services.AddScoped<IPositionQueryable, PositionQueryable>();
            services.AddScoped<IProjectDailyReportQueryable, ProjectDailyReportQueryable>();
            services.AddScoped<IProjectQueryable, ProjectQueryable>();
            services.AddScoped<IRankingQueryable, RankingQueryable>();
            services.AddScoped<IRequestQueryable, RequestQueryable>();
            services.AddScoped<IRoleFunctionQueryable, RoleFunctionQueryable>();
            services.AddScoped<IRoleQueryable, RoleQueryable>();
            services.AddScoped<IScheduleQueryable, ScheduleQueryable>();
            services.AddScoped<IStructureQueryable, StructureQueryable>();
            services.AddScoped<IUserInfoQueryable, UserInfoQueryable>();
            services.AddScoped<IVendorQueryable, VendorQueryable>();
            services.AddScoped<IWorkShiftQueryable, WorkShiftQueryable>();
            return services;
        }

        public static IServiceCollection AddTenantContext(
            this IServiceCollection services
        )
        {
            // Register ITenantContext - this will be overridden in the API layer
            // with HttpTenantContext for HTTP scenarios
            services.AddScoped<ITenantContext, StaticTenantContext>();
            return services;
        }

        public static IServiceCollection AddSeeders(
            this IServiceCollection services
        )
        {
            services.AddScoped<ICategorySeeder, CategorySeeder>();
            services.AddScoped<ICustomerTypeSeeder, CustomerTypeSeeder>();
            return services;
        }
    }
}