using Kantoku.Persistence.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class UserInfoConfiguration(string schema) : IEntityTypeConfiguration<UserInfo>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<UserInfo> builder)
    {
        builder.ToTable("user", Schema, tb => tb.<PERSON>Comment("thông tin cá nhân user"));

        builder.HasKey(e => e.UserInfoUid).HasName("user_info_pkey");

        builder.Property(e => e.UserInfoUid)
            .HasColumnName("user_uid");
        builder.Property(e => e.AccountUid)
            .HasColumnName("account_uid");
        builder.Property(e => e.Address)
            .HasColumnName("address");
        builder.Property(e => e.AvatarUrl)
            .HasColumnName("avatar_url");
        builder.Property(e => e.Birthday)
            .HasColumnName("birthday");
        builder.Property(e => e.Gender)
            .HasDefaultValue(false)
            .HasColumnName("gender");
        builder.Property(e => e.Name)
            .HasColumnName("name");
        builder.Property(e => e.Phone)
            .HasColumnName("phone");
        builder.Property(e => e.AvatarUrl)
            .HasColumnName("avatar_url");

        builder.HasOne(d => d.Account)
            .WithOne(p => p.UserInfo)
            .HasForeignKey<UserInfo>(d => d.AccountUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("user_info_account_id_fkey");
    }
} 