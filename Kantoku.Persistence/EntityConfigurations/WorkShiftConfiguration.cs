using Kantoku.Persistence.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class WorkShiftConfiguration(string schema) : IEntityTypeConfiguration<WorkShift>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<WorkShift> builder)
    {
        builder.ToTable("workshift", Schema);

        builder.HasKey(e => e.WorkShiftUid).HasName("workshift_pkey");

        builder.Property(e => e.WorkShiftUid)
            .HasColumnName("workshift_uid");
        builder.Property(e => e.WorkShiftCode)
            .HasColumnName("workshift_code");
        builder.Property(e => e.WorkShiftName)
            .HasColumnName("workshift_name");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
        builder.Property(e => e.CheckInTime)
            .HasColumnName("checkin_time");
        builder.Property(e => e.CheckOutTime)
            .HasColumnType("time")
            .HasColumnName("checkout_time");
        builder.Property(e => e.WorkShiftBreakTimes)
            .HasColumnType("jsonb")
            .HasColumnName("workshift_breaks")
            .HasConversion(
                v => Newtonsoft.Json.JsonConvert.SerializeObject(v),
                v => Newtonsoft.Json.JsonConvert.DeserializeObject<IEnumerable<WorkShiftBreakTime>>(v) ?? Enumerable.Empty<WorkShiftBreakTime>()
            ).Metadata.SetValueComparer(
                new ValueComparer<IEnumerable<WorkShiftBreakTime>>(
                    (c1, c2) => (c1 == null && c2 == null) || (c1 != null && c2 != null && c1.SequenceEqual(c2)),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()
                )
            );
        builder.Property(e => e.TotalRequiredTime)
            .HasColumnName("total_required_time");

        builder.HasOne(d => d.Org)
            .WithMany(p => p.WorkShifts)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("workshift_org_id_fkey");
    }
} 