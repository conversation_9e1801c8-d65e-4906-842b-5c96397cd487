using Kantoku.Persistence.Contexts;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Services;

/// <summary>
/// Static implementation of tenant context for background jobs, console apps, or testing
/// </summary>
public class StaticTenantContext : ITenantContext
{
    private readonly TenantInfo _tenantInfo;

    public StaticTenantContext(
        TenantInfo tenantInfo)
    {
        _tenantInfo = tenantInfo;
    }

    public Guid GetCurrentAccountUid() => _tenantInfo.AccountUid;

    public Guid GetCurrentOrgUid() => _tenantInfo.OrgUid;

    public Guid GetCurrentEmployeeUid() => _tenantInfo.EmployeeUid;

    public bool TryGetCurrentAccountUid(out Guid accountUid)
    {
        accountUid = _tenantInfo.AccountUid;
        return accountUid != Guid.Empty;
    }

    public bool TryGetCurrentOrgUid(out Guid orgUid)
    {
        orgUid = _tenantInfo.OrgUid;
        return orgUid != Guid.Empty;
    }

    public bool TryGetCurrentEmployeeUid(out Guid employeeUid)
    {
        employeeUid = _tenantInfo.EmployeeUid;
        return employeeUid != Guid.Empty;
    }
}

/// <summary>
/// Factory for creating static tenant contexts
/// </summary>
public static class StaticTenantContextFactory
{
    /// <summary>
    /// Creates a tenant context for system operations (no specific tenant)
    /// </summary>
    public static StaticTenantContext CreateSystemContext()
    {
        return new StaticTenantContext(new TenantInfo
        {
            AccountUid = Guid.Empty,
            OrgUid = Guid.Empty,
            EmployeeUid = Guid.Empty,
            IsSuperUser = true,
            IsOrgSuperUser = true
        });
    }

    /// <summary>
    /// Creates a tenant context for a specific tenant
    /// </summary>
    public static StaticTenantContext CreateTenantContext(
        Guid accountUid,
        Guid orgUid,
        Guid employeeUid)
    {
        return new StaticTenantContext(new TenantInfo
        {
            AccountUid = accountUid,
            OrgUid = orgUid,
            EmployeeUid = employeeUid,
            IsSuperUser = false,
            IsOrgSuperUser = false
        });
    }
}
