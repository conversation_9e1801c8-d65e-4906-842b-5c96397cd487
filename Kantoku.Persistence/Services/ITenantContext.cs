namespace Kantoku.Persistence.Services;

/// <summary>
/// Abstraction for accessing tenant-specific information
/// </summary>
public interface ITenantContext
{
    /// <summary>
    /// Gets the current account UID
    /// </summary>
    Guid GetCurrentAccountUid();
    
    /// <summary>
    /// Gets the current organization UID
    /// </summary>
    Guid GetCurrentOrgUid();
    
    /// <summary>
    /// Gets the current employee UID
    /// </summary>
    Guid GetCurrentEmployeeUid();
    
    /// <summary>
    /// Tries to get the current account UID
    /// </summary>
    bool TryGetCurrentAccountUid(out Guid accountUid);
    
    /// <summary>
    /// Tries to get the current organization UID
    /// </summary>
    bool TryGetCurrentOrgUid(out Guid orgUid);
    
    /// <summary>
    /// Tries to get the current employee UID
    /// </summary>
    bool TryGetCurrentEmployeeUid(out Guid employeeUid);
}

/// <summary>
/// Tenant context information
/// </summary>
public class TenantInfo
{
    public Guid AccountUid { get; set; }
    public Guid OrgUid { get; set; }
    public Guid EmployeeUid { get; set; }
    public bool IsSuperUser { get; set; }
    public bool IsOrgSuperUser { get; set; }
}
