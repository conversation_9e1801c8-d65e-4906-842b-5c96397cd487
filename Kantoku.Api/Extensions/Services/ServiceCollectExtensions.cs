using System.Reflection;
using Kantoku.Api.Utils.Attributes.Class;

namespace Kantoku.Api.Extensions.Services
{
    public static class ServiceCollectExtensions
    {
        public static IServiceCollection AddServices(this IServiceCollection services, params Assembly[] assemblies)
        {
            var assembliesToScan = assemblies.Any()
                ? assemblies
                : new[] { Assembly.GetExecutingAssembly() };

            foreach (var assembly in assembliesToScan)
            {
                // RegisterAttributedClasses<QueryableAttribute>(services, assembly);
                // RegisterAttributedClasses<RepositoryAttribute>(services, assembly);
                RegisterAttributedClasses<ServiceAttribute>(services, assembly);
                // RegisterAttributedClasses<SeederAttribute>(services, assembly);
                // RegisterBackgroundServices(services, assembly);
            }

            return services;
        }

        private static void RegisterBackgroundServices(IServiceCollection services, Assembly assembly)
        {
            var types = assembly.GetTypes()
                .Where(t => t.GetCustomAttribute<BackgroundServiceAttribute>() is not null);

            foreach (var type in types)
            {
                var attribute = type.GetCustomAttribute<BackgroundServiceAttribute>();
                if (attribute is null)
                {
                    throw new Exception($"Attribute {typeof(BackgroundServiceAttribute).Name} not found on type {type.Name}");
                }
                var lifetime = GetLifetime(attribute);

                services.AddHostedService(sp => (BackgroundService)sp.GetRequiredService(type));
            }
        }

        private static void RegisterAttributedClasses<TAttribute>(IServiceCollection services, Assembly assembly)
            where TAttribute : Attribute
        {
            var types = assembly.GetTypes()
                .Where(t => t.GetCustomAttribute<TAttribute>() is not null);

            foreach (var type in types)
            {
                var attribute = type.GetCustomAttribute<TAttribute>();
                if (attribute is null)
                {
                    throw new Exception($"Attribute {typeof(TAttribute).Name} not found on type {type.Name}");
                }
                var lifetime = GetLifetime(attribute);

                // Get the interface that matches the naming convention
                var interfaceType = type.GetInterfaces()
                    .FirstOrDefault(i => i.Name == $"I{type.Name}");

                if (interfaceType is not null)
                {
                    services.Add(new ServiceDescriptor(interfaceType, type, lifetime));
                }
                else
                {
                    services.Add(new ServiceDescriptor(type, type, lifetime));
                }
            }
        }

        private static ServiceLifetime GetLifetime(Attribute attribute)
        {
            return attribute switch
            {
                ServiceAttribute sa => sa.Lifetime,
                RepositoryAttribute ra => ra.Lifetime,
                QueryableAttribute qa => qa.Lifetime,
                SeederAttribute sa => sa.Lifetime,
                _ => ServiceLifetime.Scoped
            };
        }
    }
}