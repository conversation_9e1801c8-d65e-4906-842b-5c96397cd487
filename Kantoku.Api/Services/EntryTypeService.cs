﻿using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Repositories;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.EntryType.Request;
using Kantoku.Api.Dtos.EntryType.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Mappers.EntityMappers;
using DomainFilter = Kantoku.Persistence.Filters;
using ApiFilter = Kantoku.Api.Filters.Domains;
using Kantoku.Persistence.Filters;

namespace Kantoku.Api.Services;

public interface IEntryTypeService
{
    Task<ResultDto<EntryTypeResponseDto>> GetById(Guid entryTypeId);
    Task<ResultDto<EntryTypesResponseDto>> GetByFilter(ApiFilter.EntryTypeFilter filter);
    Task<ResultDto<EntryTypeResponseDto>> Create(CreateEntryTypeRequestDto requestDto);
    Task<ResultDto<EntryTypeResponseDto>> Update(Guid entryTypeId, UpdateEntryTypeRequestDto requestDto);
    Task<ResultDto<bool>> Delete(Guid entryTypeId);
}

[Service(ServiceLifetime.Scoped)]
public class EntryTypeService : BaseService<EntryTypeService>, IEntryTypeService
{
    private readonly IEntryTypeRepository entryTypeRepository;
    private readonly IFilterMapper<ApiFilter.EntryTypeFilter, DomainFilter.EntryTypeFilter> entryTypeFilterMapper;
    public EntryTypeService(IEntryTypeRepository entryTypeRepository,
        IFilterMapper<ApiFilter.EntryTypeFilter, DomainFilter.EntryTypeFilter> entryTypeFilterMapper,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor)
        : base(logger, httpContextAccessor)
    {
        this.entryTypeRepository = entryTypeRepository;
        this.entryTypeFilterMapper = entryTypeFilterMapper;
    }

    public async Task<ResultDto<EntryTypesResponseDto>> GetByFilter(ApiFilter.EntryTypeFilter filter)
    {
        var domainFilter = entryTypeFilterMapper.MapToDomain(filter);
        var (entryTypes, total) = await entryTypeRepository.GetByFilter(domainFilter, new EntryTypeQueryableOptions());
        if (entryTypes is null || !entryTypes.Any() || total == 0)
        {
            logger.Error("EntryType not found");
            return new ErrorResultDto<EntryTypesResponseDto>(ResponseCodeConstant.ENTRYTYPE_NOT_EXIST);
        }
        var result = entryTypes.ToEntryTypesResponseDto(filter.PageNum, filter.PageSize, total);
        return new SuccessResultDto<EntryTypesResponseDto>(result);
    }

    public async Task<ResultDto<EntryTypeResponseDto>> GetById(Guid entryTypeId)
    {
        var entryType = await entryTypeRepository.GetById(entryTypeId, new EntryTypeQueryableOptions());
        if (entryType is null)
        {
            logger.Error("EntryType not found");
            return new ErrorResultDto<EntryTypeResponseDto>(ResponseCodeConstant.ENTRYTYPE_NOT_EXIST);
        }
        var result = entryType.ToEntryTypeResponseDto();
        return new SuccessResultDto<EntryTypeResponseDto>(result);
    }

    public async Task<ResultDto<EntryTypeResponseDto>> Create(CreateEntryTypeRequestDto requestDto)
    {
        if (!GetCurrentOrgGuid(out var orgUid))
        {
            logger.Error("Organization not found");
            return new ErrorResultDto<EntryTypeResponseDto>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        var newEntryType = requestDto.ToEntity(orgUid);
        if (newEntryType is null)
        {
            logger.Error("Error creating entry type");
            return new ErrorResultDto<EntryTypeResponseDto>(ResponseCodeConstant.ENTRYTYPE_CREATE_FAILED);
        }

        var createdEntryType = await entryTypeRepository.Create(newEntryType, new EntryTypeQueryableOptions());
        if (createdEntryType is null)
        {
            logger.Error("EntryType create failed");
            return new ErrorResultDto<EntryTypeResponseDto>(ResponseCodeConstant.ENTRYTYPE_CREATE_FAILED);
        }
        var result = createdEntryType.ToEntryTypeResponseDto();
        return new SuccessResultDto<EntryTypeResponseDto>(result);
    }

    public async Task<ResultDto<EntryTypeResponseDto>> Update(Guid entryTypeId, UpdateEntryTypeRequestDto requestDto)
    {
        var existEntryType = await entryTypeRepository.GetById(entryTypeId, new EntryTypeQueryableOptions());
        if (existEntryType is null)
        {
            logger.Error("EntryType not found");
            return new ErrorResultDto<EntryTypeResponseDto>(ResponseCodeConstant.ENTRYTYPE_NOT_EXIST);
        }
        existEntryType.UpdateFromDto(requestDto);

        var updatedEntryType = await entryTypeRepository.Update(existEntryType, new EntryTypeQueryableOptions());
        if (updatedEntryType is null)
        {
            logger.Error("EntryType update failed");
            return new ErrorResultDto<EntryTypeResponseDto>(ResponseCodeConstant.ENTRYTYPE_UPDATE_FAILED);
        }
        var result = existEntryType.ToEntryTypeResponseDto();
        return new SuccessResultDto<EntryTypeResponseDto>(result);
    }

    public async Task<ResultDto<bool>> Delete(Guid entryTypeId)
    {

        var entryType = await entryTypeRepository.GetById(entryTypeId, new EntryTypeQueryableOptions());
        if (entryType is null)
        {
            logger.Error("EntryType not found");
            return new ErrorResultDto<bool>(ResponseCodeConstant.ENTRYTYPE_NOT_EXIST);
        }
        entryType.IsDeleted = true;
        var isDeleted = await entryTypeRepository.Update(entryType);
        if (!isDeleted)
        {
            logger.Error("EntryType delete failed");
            return new ErrorResultDto<bool>(ResponseCodeConstant.ENTRYTYPE_DELETE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }
}
