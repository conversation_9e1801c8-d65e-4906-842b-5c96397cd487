using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Dtos.Customer.Request;
using Kantoku.Api.Dtos.Customer.Response;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Persistence.Repositories;
using Kantoku.Api.Dtos.File;
using Kantoku.Persistence.Queryables;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Utils.Mappers.EntityMappers;
using Kantoku.Persistence.Filters;
using ApiFilter = Kantoku.Api.Filters.Domains;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Api.Services;

public interface ICustomerService
{
    Task<ResultDto<CustomersResponseDto>> GetCustomerByFilter(ApiFilter.CustomerFilter filter);
    Task<ResultDto<byte[]>> GetLogo(Guid customerId, string orgId);
    Task<ResultDto<CustomerDetailResponseDto>> GetCustomerById(Guid customerId, string? keyword, int pageNum, int pageSize);
    Task<ResultDto<CustomerResponseDto>> CreateCustomer(CreateCustomerRequestDto requestDto);
    Task<ResultDto<CustomerResponseDto>> UpdateCustomer(Guid customerId, UpdateCustomerRequestDto requestDto);
    Task<ResultDto<bool>> DeleteCustomer(Guid customerId);
}

[Service(ServiceLifetime.Scoped)]
public class CustomerService : BaseService<CustomerService>, ICustomerService
{
    private readonly ICustomerRepository customerRepository;
    private readonly IProjectRepository projectRepository;
    private readonly IFileService fileService;
    private readonly IFilterMapper<ApiFilter.CustomerFilter, DomainFilter.CustomerFilter> filterMapper;


    public CustomerService(
        ICustomerRepository customerRepository,
        IProjectRepository projectRepository,
        IFileService fileService,
        IFilterMapper<ApiFilter.CustomerFilter, DomainFilter.CustomerFilter> filterMapper,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor) : base(logger, httpContextAccessor)
    {
        this.customerRepository = customerRepository;
        this.fileService = fileService;
        this.projectRepository = projectRepository;
        this.filterMapper = filterMapper;
    }

    public async Task<ResultDto<CustomersResponseDto>> GetCustomerByFilter(ApiFilter.CustomerFilter filter)
    {
        var options = new CustomerQueryableOptions
        {
            IncludedCustomerType = true,
        };
        var domainFilter = filterMapper.MapToDomain(filter);
        var (customers, total) = await customerRepository.GetByFilter(domainFilter, options);
        if (customers is null || !customers.Any() || total == 0)
        {
            logger.Information("No customers found");
            return new ErrorResultDto<CustomersResponseDto>(ResponseCodeConstant.CUSTOMER_NOT_EXIST);
        }

        var res = customers.ToCustomersResponseDto(filter.PageNum, filter.PageSize, total, GetCurrentLanguageCode());
        return new SuccessResultDto<CustomersResponseDto>(res);
    }

    public async Task<ResultDto<CustomerDetailResponseDto>> GetCustomerById(Guid customerId, string? keyword, int pageNum, int pageSize)
    {
        var customer = await customerRepository.GetById(customerId, new CustomerQueryableOptions
        {
            IncludedProject = true,
            IncludedCustomerType = true,
        });
        if (customer is null)
        {
            logger.Error("Customer {CustomerId} not found", customerId);
            return new ErrorResultDto<CustomerDetailResponseDto>(ResponseCodeConstant.CUSTOMER_NOT_EXIST);
        }
        var (projects, total) = await projectRepository.GetByFilter(new DomainFilter.ProjectFilter
        {
            CustomerUid = customerId,
            Keyword = keyword,
            PageNum = pageNum,
            PageSize = pageSize
        }, new ProjectQueryableOptions());

        var res = customer.ToCustomerDetailResponseDto(projects, GetCurrentLanguageCode(), pageNum, pageSize, total);
        return new SuccessResultDto<CustomerDetailResponseDto>(res);
    }

    public async Task<ResultDto<byte[]>> GetLogo(Guid customerId, string orgId)
    {
        var customer = await customerRepository.GetById(customerId, new CustomerQueryableOptions());
        if (customer is null)
        {
            logger.Error("Customer {CustomerId} not found", customerId);
            return new ErrorResultDto<byte[]>(ResponseCodeConstant.CUSTOMER_NOT_EXIST);
        }
        if (customer.LogoUrl is null)
        {
            logger.Error("Customer {CustomerId} has no logo", customerId);
            return new ErrorResultDto<byte[]>(ResponseCodeConstant.CUSTOMER_LOGO_NOT_EXIST);
        }
        var logo = await DownloadLogo(customer.LogoUrl, orgId);
        if (logo is null || logo.Length == 0)
        {
            logger.Error("Customer {CustomerId} logo not found", customerId);
            return new ErrorResultDto<byte[]>(ResponseCodeConstant.CUSTOMER_LOGO_NOT_EXIST);
        }
        return new SuccessResultDto<byte[]>(logo);
    }

    public async Task<ResultDto<CustomerResponseDto>> CreateCustomer(CreateCustomerRequestDto requestDto)
    {
        if (!GetCurrentOrgGuid(out var orgUid))
        {
            logger.Error("Failed to get current org uid");
            return new ErrorResultDto<CustomerResponseDto>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        var newCustomer = requestDto.ToEntity(orgUid);
        if (newCustomer is null)
        {
            logger.Error("Failed to create customer");
            return new ErrorResultDto<CustomerResponseDto>(ResponseCodeConstant.CUSTOMER_CREATE_FAILED);
        }
        if (requestDto.Logo is not null)
        {
            var logoResponse = await UploadLogo(newCustomer.CustomerUid.ToString(), requestDto.Logo);
            if (logoResponse is not null)
            {
                newCustomer.LogoUrl = logoResponse.FileUrl;
            }
        }
        var createdCustomer = await customerRepository.Create(newCustomer);
        if (createdCustomer is null)
        {
            logger.Error("Failed to create customer");
            return new ErrorResultDto<CustomerResponseDto>(ResponseCodeConstant.CUSTOMER_CREATE_FAILED);
        }

        var result = createdCustomer.ToCustomerResponseDto(GetCurrentLanguageCode());
        return new SuccessResultDto<CustomerResponseDto>(result);
    }

    public async Task<ResultDto<CustomerResponseDto>> UpdateCustomer(Guid customerId, UpdateCustomerRequestDto requestDto)
    {
        var existCustomer = await customerRepository.GetById(customerId, new CustomerQueryableOptions());
        if (existCustomer is null)
        {
            logger.Error("Customer {CustomerId} not found", customerId);
            return new ErrorResultDto<CustomerResponseDto>(ResponseCodeConstant.CUSTOMER_NOT_EXIST);
        }

        existCustomer.UpdateFromDto(requestDto);

        if (requestDto.Logo is not null)
        {
            await UploadLogo(existCustomer.CustomerUid.ToString(), requestDto.Logo);
        }
        var updatedCustomer = await customerRepository.Update(existCustomer);
        if (updatedCustomer is null)
        {
            logger.Error("Failed to update customer {CustomerId}", customerId);
            return new ErrorResultDto<CustomerResponseDto>(ResponseCodeConstant.CUSTOMER_UPDATE_FAILED);
        }
        var result = updatedCustomer.ToCustomerResponseDto(GetCurrentLanguageCode());
        return new SuccessResultDto<CustomerResponseDto>(result);
    }

    public async Task<ResultDto<bool>> DeleteCustomer(Guid customerId)
    {
        var customer = await customerRepository.GetById(customerId, new CustomerQueryableOptions());
        if (customer is null)
        {
            logger.Error("Customer {CustomerId} not found", customerId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.CUSTOMER_NOT_EXIST, false);
        }
        customer.IsDeleted = true;
        var isDeleted = await customerRepository.Update(customer);
        if (isDeleted is null)
        {
            logger.Error("Failed to delete customer {CustomerId}", customerId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.CUSTOMER_DELETE_FAILED, false);
        }
        return new SuccessResultDto<bool>(true);
    }

    private async Task<FileMetadataResponseDto?> UploadLogo(string customerUid, IFormFile logo)
    {
        try
        {
            var path = StorageConstant.CustomerLogo(customerUid);
            var fileName = "logo.jpg";
            var objectName = path + fileName;
            var fileMetadata = await fileService.UploadFileAsync(logo, objectName);
            if (fileMetadata is null || fileMetadata.Data is null || fileMetadata.Data.FileUrl is null)
            {
                logger.Error("Upload logo failed");
                return null;
            }
            return fileMetadata.Data;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error uploading logo");
            return new FileMetadataResponseDto();
        }
    }

    private async Task<byte[]> DownloadLogo(string logoUrl, string orgId)
    {
        try
        {
            var file = await fileService.DownloadFile(logoUrl, orgId);
            if (file is null || file.Data is null)
            {
                logger.Error("Download logo failed");
                return [];
            }
            return file.Data.DataAsBytes;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting logo as base64");
            return [];
        }
    }
}
