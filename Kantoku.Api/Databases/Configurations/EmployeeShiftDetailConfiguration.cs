using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Kantoku.Api.Databases.Configurations;

public class EmployeeShiftDetailConfiguration(string schema) : IEntityTypeConfiguration<EmployeeShiftDetail>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<EmployeeShiftDetail> builder)
    {
        builder.HasKey(e => e.EmployeeShiftDetailUid).HasName("employee_shift_detail_pkey");

        builder.ToTable("employee_shift_detail", Schema, tb => tb.HasComment("Thông tin ca làm việc"));

        builder.Property(e => e.EmployeeShiftDetailUid)
            .HasColumnName("employee_shift_detail_uid");
        // builder.Property(e => e.EmployeeShiftUid)
        //     .HasColumnName("employee_shift_uid");
        builder.Property(e => e.ProjectUid)
            .HasColumnName("project_uid");
        builder.Property(e => e.OccuredAt)
            .HasColumnName("occured_at");
        builder.Property(e => e.Latitude)
            .HasColumnName("latitude");
        builder.Property(e => e.Longitude)
            .HasColumnName("longitude");
        builder.Property(e => e.Location)
            .HasColumnName("location");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.IsActive)
            .HasColumnName("is_active");

        // builder.HasOne(d => d.EmployeeShift)
        //     .WithMany(p => p.EmployeeShiftDetails)
        //     .HasForeignKey(d => d.EmployeeShiftUid)
        //     .OnDelete(DeleteBehavior.ClientSetNull)
        //     .HasConstraintName("employee_shift_detail_employee_shift_uid_fkey");

        builder.HasOne(d => d.Project).WithMany(p => p.EmployeeShiftDetails)
            .HasForeignKey(d => d.ProjectUid)
            .HasConstraintName("employee_shift_detail_project_uid_fkey");
    }
}
