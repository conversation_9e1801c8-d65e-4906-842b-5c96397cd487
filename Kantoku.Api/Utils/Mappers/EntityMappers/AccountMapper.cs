using Kantoku.Api.Dtos.Account.Request;
using Kantoku.Api.Dtos.Account.Response;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Helpers;
using Kantoku.Persistence.Models;


namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class AccountMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps an Account entity to an AccountResponseDto
    /// </summary>
    /// <param name="account">The Account entity to map</param>
    /// <param name="userInfo">The UserInfo entity to map</param>
    /// <returns>The mapped AccountResponseDto</returns>    
    public static AccountResponseDto ToAccountResponseDto(this Account account, UserInfo userInfo)
    {
        if (account == null)
            return new AccountResponseDto();

        return new AccountResponseDto
        {
            AccountUid = account.AccountUid.ToString(),
            UserName = account.UserInfo?.Name,
            Email = account.Email,
            LoginId = account.LoginId
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateAccountRequestDto to an Account entity
    /// </summary>
    /// <param name="dto">The CreateAccountRequestDto to map</param>
    /// <returns>The mapped Account entity</returns>    
    public static Account? ToEntity(this CreateAccountRequestDto dto)
    {
        if (dto == null)
            return null;

        var entity = new Account
        {
            AccountUid = GuidHelper.GenerateUUIDv7(),
            LoginId = dto.LoginId,
            Email = dto.Email,
            Password = StringHelper.HashPassword(dto.Password),
            HashedPassword = dto.HashedPassword is not null ? StringHelper.HashPassword(dto.HashedPassword) : null,
            AccountType = Persistence.Models.AccountTypeConstant.USER,
            IsDeleted = false,
            IsLocked = false,
        };

        // Temporary mapping for UserInfo until UserInfoMapper is created
        // This assumes CreateAccountRequestDto.UserInfoRequestDto has Name, Address, Phone, Gender, Birthday
        if (dto.UserInfo != null)
        {
            entity.UserInfo = new UserInfo
            {
                UserInfoUid = GuidHelper.GenerateUUIDv7(), // Assuming UserInfo needs its own GUID
                AccountUid = entity.AccountUid, // Link back to account
                Name = dto.UserInfo.Name,
                Address = dto.UserInfo.Address,
                Phone = dto.UserInfo.Phone,
                Gender = dto.UserInfo.Gender,
                Birthday = dto.UserInfo.Birthday != null ? DateOnly.Parse(dto.UserInfo.Birthday) : null,
                // Other UserInfo fields might need default values or be set elsewhere
            };
        }


        return entity;
    }

    /// <summary>
    /// Maps an UpdateAccountRequestDto to an Account entity
    /// </summary>
    /// <param name="entity">The Account entity to update</param>
    /// <param name="dto">The UpdateAccountRequestDto to map</param>
    public static void UpdateFromDto(this Account entity, UpdateAccountRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        if (dto.LoginId != null)
            entity.LoginId = dto.LoginId;

        if (dto.Email != null)
            entity.Email = dto.Email;
    }

    #endregion
}