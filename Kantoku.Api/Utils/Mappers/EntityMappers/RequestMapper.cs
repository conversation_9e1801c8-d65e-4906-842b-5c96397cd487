using Kantoku.Persistence.Models;
using Kantoku.Api.Dtos.EmployeeRequest.Request;
using Kantoku.Api.Dtos.EmployeeRequest.Response;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Helpers;
using LeaveTypeConstants = Kantoku.Persistence.Models.LeaveTypeConstants;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class RequestMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a Request entity to a RequestInfoResponseDto
    /// </summary>
    /// <param name="request">The Request entity to map</param>
    /// <param name="languageCode">The language code to use for the mapping</param>
    /// <returns>A RequestInfoResponseDto representing the Request entity</returns> 
    public static RequestInfoResponseDto ToRequestInfoResponseDto(this Request request, string languageCode)
    {
        if (request == null)
            return new RequestInfoResponseDto();

        var duration = new RequestDurationResponseDto();
        TimeSpan span = request.RequestTo - request.RequestFrom;

        if (span.TotalDays >= 1)
            duration.Days = (float)Math.Floor(span.TotalDays);

        if (span.Hours > 0)
            duration.Hours = span.Hours;

        if (span.Minutes > 0)
            duration.Minutes = span.Minutes;

        return new RequestInfoResponseDto
        {
            RequestId = request.RequestUid.ToString(),
            RequestUserName = request.Author?.EmployeeName,
            RequestFrom = request.RequestFrom.ToString("yyyy-MM-dd HH:mm:ss"),
            RequestTo = request.RequestTo.ToString("yyyy-MM-dd HH:mm:ss"),
            RequestTypeCode = request.RequestTypeCode,
            RequestTypeName = request.RequestType?.TranslatedRequestType?
                .FirstOrDefault(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase))
                ?.RequestTypeName,
            IsUserRequestedLeave = request.IsUserRequestedLeave,
            Description = request.Description,
            StatusCode = request.StatusCode,
            StatusName = request.Status?.TranslatedStatus?
                .FirstOrDefault(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase))
                ?.StatusName,
            CreateUserName = request.Author?.EmployeeName,
            ProjectId = request.ProjectUid?.ToString(),
            ProjectName = request.Project?.ProjectName,

            Approver1Name = request.Approver1?.EmployeeName,
            Approver1Status = request.Approver1Status,
            Approver1Notes = request.Approver1Notes,
            Approver1Time = request.Approver1Time?.ToString("yyyy-MM-dd HH:mm:ss"),

            Approver2Name = request.Approver2?.EmployeeName,
            Approver2Status = request.Approver2Status,
            Approver2Notes = request.Approver2Notes,
            Approver2Time = request.Approver2Time?.ToString("yyyy-MM-dd HH:mm:ss"),

            CreateTime = request.CreatedTime?.ToString("yyyy-MM-dd HH:mm:ss"),
            UpdateTime = request.LastModifiedTime?.ToString("yyyy-MM-dd HH:mm:ss")
        };
    }

    /// <summary>
    /// Maps a collection of Request entities to a RequestsInfoResponseDto
    /// </summary>
    /// <param name="requests">The collection of Request entities to map</param>
    /// <param name="pageNum">The page number to use for the mapping</param>
    /// <param name="pageSize">The page size to use for the mapping</param>
    /// <param name="totalRecords">The total number of records to use for the mapping</param>
    /// <param name="languageCode">The language code to use for the mapping</param>
    /// <returns>A RequestsInfoResponseDto representing the collection of Request entities</returns>
    public static RequestsInfoResponseDto ToRequestsInfoResponseDto(
        this IEnumerable<Request> requests,
        int pageNum,
        int pageSize,
        int totalRecords,
        string languageCode)
    {
        if (requests == null)
            return new RequestsInfoResponseDto
            {
                PageIndex = pageNum,
                PageSize = pageSize,
                TotalRecords = 0,
                Items = []
            };

        return new RequestsInfoResponseDto
        {
            Items = requests.Select(r => r.ToRequestInfoResponseDto(languageCode)),
            PageIndex = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateEmployeeRequestRequestDto to a Request entity
    /// </summary>
    public static Request? ToEntity(this CreateEmployeeRequestRequestDto dto, Guid authorUid)
    {
        if (dto == null)
            return null;

        if (!DateTime.TryParse(dto.RequestFrom, out var requestFrom) ||
            !DateTime.TryParse(dto.RequestTo, out var requestTo))
            return null;

        var request = new Request
        {
            RequestUid = GuidHelper.GenerateUUIDv7(),
            AuthorUid = authorUid,
            ProjectUid = Guid.TryParse(dto.ProjectId, out var projectUid)
                ? projectUid
                : null,
            RequestFrom = requestFrom,
            RequestTo = requestTo,
            RequestTypeCode = dto.RequestTypeCode,
            Description = dto.Description,
            StatusCode = StatusConstants.PENDING,
            IsUserRequestedLeave = dto.IsUserRequestedLeave,
            IsDeleted = false,
        };

        if (!string.IsNullOrEmpty(dto.LeaveTypeCode))
        {
            if (dto.LeaveTypeCode == LeaveTypeConstants.PAID_ORG)
                request.IsUserRequestedLeave = false;
            else if (dto.LeaveTypeCode == LeaveTypeConstants.PAID)
                request.IsUserRequestedLeave = true;
        }

        return request;
    }

    /// <summary>
    /// Updates a Request entity from an UpdateEmployeeRequestRequestDto
    /// </summary>
    public static void UpdateFromDto(this Request entity, UpdateEmployeeRequestRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        if (!string.IsNullOrEmpty(dto.RequestFrom) &&
            DateTime.TryParse(dto.RequestFrom, out var requestFrom))
            entity.RequestFrom = requestFrom;

        if (!string.IsNullOrEmpty(dto.RequestTo) &&
            DateTime.TryParse(dto.RequestTo, out var requestTo))
            entity.RequestTo = requestTo;

        if (!string.IsNullOrEmpty(dto.RequestTypeCode))
            entity.RequestTypeCode = dto.RequestTypeCode;

        if (!string.IsNullOrEmpty(dto.LeaveTypeCode))
        {
            if (dto.LeaveTypeCode == LeaveTypeConstants.PAID_ORG)
                entity.IsUserRequestedLeave = false;
            else if (dto.LeaveTypeCode == LeaveTypeConstants.PAID)
                entity.IsUserRequestedLeave = true;
        }

        if (dto.IsUserRequestedLeave != null)
            entity.IsUserRequestedLeave = dto.IsUserRequestedLeave;

        if (!string.IsNullOrEmpty(dto.ProjectId) &&
            Guid.TryParse(dto.ProjectId, out var projectUid))
            entity.ProjectUid = projectUid;

        if (dto.Description != null)
            entity.Description = dto.Description;
    }

    public static void ApproveRequest(
        this Request request,
        Guid approverUid,
        bool isApprover1,
        bool isApproved,
        string? notes = null)
    {
        if (request == null)
            return;

        if (isApprover1)
        {
            request.Approver1Uid = approverUid;
            request.Approver1Status = isApproved;
            request.Approver1Notes = notes;
            request.Approver1Time = DateTime.Now;
        }
        else
        {
            request.Approver2Uid = approverUid;
            request.Approver2Status = isApproved;
            request.Approver2Notes = notes;
            request.Approver2Time = DateTime.Now;
        }
    }

    #endregion
}